const model = 'api::core-country.core-country';

export default ({ strapi }) => {
  const query = strapi.entityService;

  const listCountriesOld = async ({
    page,
    pageSize,
    text,
    sortBy = "name",
    sortOrder = "asc",
    fieldToSearch = "",
    activeServiceName,
    activeServiceValue,
    locale = 'en'
  }) => {
    const filters: Record<string, any> = {};
    if (text) {
      if (fieldToSearch) {
        filters[fieldToSearch] = { "$containsi": text };
      } else {
        filters.$or = [
          {
            name: { "$containsi": text },
          },
        ]
      }
    }
    if (activeServiceName) {
      filters[activeServiceName] = activeServiceValue === 'true' || activeServiceValue === true;
    }
    let sort: Record<string, any> = {};
    if (sortBy) {
      sort = { [sortBy]: sortOrder === 'asc' ? 'asc' : 'desc' };
    }

    const getCountries = query.findMany(model, {
      filters,
      locale,
      sort,
      start: (page - 1) * pageSize,
      limit: pageSize,
    })
    const promises: any = [getCountries];

    const getTotal = query.count(model, { filters });
    promises.push(getTotal);

    const [
      countriesFound,
      totalFound
    ] = await Promise.all(promises);

    return {
      countriesFound,
      totalFound,
    }
  }

  const listCountries = async ({
    page = 1,
    pageSize = 10,
    text,
    sortBy = "name",
    sortOrder = "asc",
    fieldToSearch = "",
    activeServiceName,
    activeServiceValue,
    locale = 'en',
  }) => {
    // normalizar params
    page = Math.max(1, page || 1);
    pageSize = Math.max(1, pageSize || 10);
    const start = (page - 1) * pageSize;

    // build country filters (text search)
    const filters: any = {};
    if (text) {
      if (fieldToSearch) {
        filters[fieldToSearch] = { $containsi: text };
      } else {
        filters.$or = [{ name: { $containsi: text } }];
      }
    }

    // Si piden filtrar por servicio, buscamos primero en country-service-type
    if (activeServiceName) {
      const active = activeServiceValue === 'true' || activeServiceValue === true;

      const cstRows = await strapi.db
        .query('api::country-service-type.country-service-type')
        .findMany({
          where: {
            active,
            service: { slug: activeServiceName },
          },
          populate: { country: { fields: ['id'] } },
        });

      const countryIds = Array.from(
        new Set(
          cstRows
            .map(r => r.country && r.country.id)
            .filter(Boolean)
        )
      );

      // si no hay países que cumplan, devolvemos vacío rápidamente
      if (!countryIds.length) {
        return { countriesFound: [], totalFound: 0 };
      }

      // añadir filter por ids (preserva otros filtros de texto)
      filters.id = { $in: countryIds };
    }

    // ordenar
    const sort = sortBy ? { [sortBy]: sortOrder === 'asc' ? 'asc' : 'desc' } : {};

    // ejecutar consultas en paralelo
    const getCountries = query.findMany(model, {
      filters,
      locale,
      sort,
      start,
      limit: pageSize,
    });

    const getTotal = query.count(model, { filters, locale });

    const [countriesFound, totalFound] = await Promise.all([getCountries, getTotal]);

    return {
      countriesFound,
      totalFound,
    };
  };


  return {
    listCountries
  }
}
