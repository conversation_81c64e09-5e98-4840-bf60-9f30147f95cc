import { Strapi } from '@strapi/strapi';

// TODO update to replace remittanceActive, mobileTopUpActive, giftCardActive by countryServiceType.active
const GenericPageTypes = {
    "RemittanceCountry": {
        remittanceActive: true
    },
    "Top-UpCountry": {
        mobileTopUpActive: true
    },
    "GiftCardCountry": {
        giftCardActive: true
    },
};

const CountryPageTypes = [
    "RemittanceCountryCustom",
    "Top-UpCountryCustom",
    "GiftCardCountryCustom",
]

const sanitizeCountryName = (countryName = '') => {
    if (!countryName) {
        return '';
    }

    // Normalize the string to remove diacritics (like accents)
    const normalized = countryName.normalize("NFD").replace(/[\u0300-\u036f]/g, "");

    // Replace spaces with hyphens
    const hyphenated = normalized?.replace(/\s+/g, "-") || '';

    // Remove all non-alphanumeric characters except hyphens
    const slug = hyphenated?.replace(/[^a-zA-Z0-9-]/g, "") || '';

    // Convert to lowercase for URL consistency
    return slug?.toLowerCase() || '';
}

export default ({ strapi }: { strapi: Strapi }) => {
    // TODO remove this deprecated
    const updateUrlExisting = async (simplePage: any) => {
        try {
            // TODO remove this function as it is deprecated
            if (simplePage) {
                return;
            }
            if (!simplePage?.type.includes('Country')) {
                return;
            }

            const page = await strapi.entityService?.findOne('api::page.page' as any, simplePage.id, { populate: ['country'] });
            // console.log('updateUrlExisting page: ', JSON.stringify(page, null, 2));
            console.log('updateUrlExisting page:', page?.title || 'no page')
            if (!page) {
                console.log('updateUrlExisting error: no page found');
                return
            }

            let prefixURL = page.url;
            console.log('updateUrlExisting prefixURL:', prefixURL || 'no url');
            console.log('updateUrlExisting page.type:', page.type || 'no page.type');
            console.log('updateUrlExisting page.locale:', page.locale || 'no page.locale');
            // console.log('updateUrlExisting page.country: ', page.country);

            const isCustom = page.type?.includes('Custom');
            console.log('updateUrlExisting isCustom: ', isCustom);
            const pageType = page.type.replace('Custom', '');
            console.log('updateUrlExisting pageType: ', pageType);
            const language = page.locale;
            console.log('updateUrlExisting language: ', language);

            if (isCustom) {
                let country = page.country?.[0];
                if (country && country.locale !== language) {
                    const countriesFound = await strapi.entityService?.findMany('api::core-country.core-country', {
                        filters: {
                            codeAlpha2: country.codeAlpha2,
                        },
                        locale: language
                    }) || [];
                    country = countriesFound[0]
                }

                if (!country) {
                    console.log('updateUrlExisting error: no country found');
                    return;
                }
                // console.log('updateUrlExisting country: ', country);

                const urlFounds: any = await strapi.entityService?.findMany('api::url.url', {
                    filters: {
                        pageType: pageType,
                        country: {
                            codeAlpha2: country.codeAlpha2,
                            locale: language
                        }
                    },
                    locale: language,
                    populate: ['country'],
                    limit: 1
                }) || [];
                // console.log('urlFounds: ', urlFounds);

                const urlFound = urlFounds[0];
                // console.log('urlFound: ', urlFound);

                const suffixURL = urlFound?.suffixURL || sanitizeCountryName(country.name);
                if (urlFound) {
                    await strapi.entityService?.update('api::url.url', urlFound.id, {
                        data: {
                            urlType: 'Existing' as any,
                            prefixURL: urlFound.prefixURL,
                            suffixURL,
                        } as any
                    });
                } else {
                    const parentsPageFound = await strapi.entityService?.findMany('api::page.page', {
                        filters: {
                            type: pageType,
                        },
                        locale: language,
                        limit: 1,
                    }) || [];
                    const parentPageFound = parentsPageFound[0];
                    if (!parentPageFound) {
                        console.log(`updateUrlExisting error: no ${pageType} page found`);
                        return;
                    }
                    prefixURL = parentPageFound.url;
                    await strapi.entityService?.create('api::url.url', {
                        data: {
                            urlType: 'Existing' as any,
                            pageType: pageType,
                            locale: language,
                            prefixURL,
                            suffixURL,
                            country: { set: [{ id: country.id }] },
                        } as any
                    })
                }
            } else {
                const urlFounds: any = await strapi.entityService?.findMany('api::url.url', {
                    filters: {
                        pageType: pageType,
                    },
                    locale: language,
                    populate: ['country'],
                    limit: 300,
                });
                // console.log('urlsFound:', JSON.stringify(urlFounds, null, 2));

                const customPageCountries = await strapi.entityService?.findMany('api::page.page', {
                    filters: {
                        type: `${pageType}Custom`,
                    },
                    locale: language,
                    populate: ['country']
                });
                const customCountryIds = customPageCountries?.map((p) => p.country?.[0]?.codeAlpha2);

                let filters: any = {
                    ...GenericPageTypes[pageType]
                };

                // TODO update to replace remittanceActive, mobileTopUpActive, giftCardActive by countryServiceType.active
                const activeServiceCountries = await strapi.entityService?.findMany('api::core-country.core-country', {
                    filters,
                    locale: language
                });
                // console.log('updateUrlExisting activeServiceCountries: ', JSON.stringify(activeServiceCountries, null, 2));

                const leftCountries = activeServiceCountries?.filter((c) => !customCountryIds.includes(c.codeAlpha2));

                for (const country of leftCountries) {
                    const suffixURL = sanitizeCountryName(country?.name);
                    const urlFound = urlFounds.find((url) => url.country?.codeAlpha2 === country.codeAlpha2);
                    // console.log('urlFound: ', urlFound, country.id, country.name)
                    if (!urlFound) {
                        try {
                            await strapi.entityService?.create('api::url.url', {
                                data: {
                                    urlType: 'Referred' as any,
                                    pageType: pageType,
                                    locale: language,
                                    country: { set: [{ id: country.id }] },
                                    prefixURL: prefixURL,
                                    suffixURL: suffixURL,
                                } as any
                            })
                        } catch (error) {
                            if (error.message?.includes('This attribute must be unique')) {
                                console.info('updateUrlExisting create url duplicate')
                            } else {
                                console.log('updateUrlExisting create url fail', error);
                            }
                        }
                    } else {
                        await strapi.entityService?.update('api::url.url', urlFound.id, {
                            data: {
                                prefixURL: prefixURL,
                                suffixURL: suffixURL,
                                country: { set: [{ id: country.id }] }
                            } as any
                        })
                    }
                }

                for (const url of urlFounds) {
                    console.log('urlfound: ', url.url);
                    let country = url.country;
                    if (!country) {
                        continue;
                    }

                    const prefixURL = page.url;
                    const suffixURL = sanitizeCountryName(country.name);
                    console.log('updating url: ', prefixURL, suffixURL);
                    await strapi.entityService?.update('api::url.url', url.id, {
                        data: {
                            prefixURL: prefixURL,
                            suffixURL: suffixURL,
                            url: `${prefixURL}-${suffixURL}`,
                            country: { set: [{ id: country.id }] }
                        } as any
                    })
                }
            }
        } catch (error) {
            console.log('error: ', error);
        }
    }

    const getUrlsByCountry = async ({
        country,
        activeServices = ['remittance', 'mobiletopup', 'giftcard']
    }) => {
        // console.log(`country name: ${country.name}, codealpha2:${country.codeAlpha2}`);
        const lang = country.locale

        const isCountryActiveServicesPromise = strapi.entityService?.findMany('api::country-service-type.country-service-type', {
            filters: {
                country: { codeAlpha2: country.codeAlpha2 },
                active: true
            },
            populate: ['country', 'service'],
            locale: lang,
        });
        const urlsFoundPromise = strapi.entityService?.findMany('api::url.url', {
            filters: {
                url: { $notNull: true },
                country: {
                    codeAlpha2: country.codeAlpha2,
                    locale: lang               
                }
            },
            populate: ['country'],
            locale: lang,
        }) || [];

        const [
            isCountryActiveServices,
            urlsFound
        ] = await Promise.all([
            isCountryActiveServicesPromise,
            urlsFoundPromise
        ]);

        const finalUrls: any = {};

        isCountryActiveServices?.forEach((countryServiceType) => {
            const serviceSlug = countryServiceType.service.slug || countryServiceType.service.name?.toLowerCase().trim() || '';
            // console.log('countryServiceType: ', JSON.stringify(countryServiceType));
            const urlFound = urlsFound?.
                filter((url) => {
                    const pageType = url.pageType.replace('Country', '').replace('Custom', '').toLowerCase();
                    // console.log('url: ', activeServices, pageType, activeServices.includes(pageType));
                    return activeServices.includes(pageType) || (pageType.includes('top-up') && activeServices.includes('mobiletopup'));
                })
                .find((url) => {
                    const pageType = url.pageType.replace('Country', '').replace('Custom', '').toLowerCase();
                    // console.log('url: ', serviceSlug, pageType, pageType === serviceSlug);
                    return pageType === serviceSlug || (pageType.includes('top-up') && serviceSlug.includes('mobiletopup'));
                });

            if (urlFound) {
                finalUrls[`${serviceSlug}Url`] = urlFound.url;
            }
        })

        return finalUrls;
    }

    return {
        updateUrlExisting,
        getUrlsByCountry
    }
}