"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// TODO update to replace remittanceActive, mobileTopUpActive, giftCardActive by countryServiceType.active
const GenericPageTypes = {
    "RemittanceCountry": {
        remittanceActive: true
    },
    "Top-UpCountry": {
        mobileTopUpActive: true
    },
    "GiftCardCountry": {
        giftCardActive: true
    },
};
const CountryPageTypes = [
    "RemittanceCountryCustom",
    "Top-UpCountryCustom",
    "GiftCardCountryCustom",
];
const sanitizeCountryName = (countryName = '') => {
    if (!countryName) {
        return '';
    }
    // Normalize the string to remove diacritics (like accents)
    const normalized = countryName.normalize("NFD").replace(/[\u0300-\u036f]/g, "");
    // Replace spaces with hyphens
    const hyphenated = (normalized === null || normalized === void 0 ? void 0 : normalized.replace(/\s+/g, "-")) || '';
    // Remove all non-alphanumeric characters except hyphens
    const slug = (hyphenated === null || hyphenated === void 0 ? void 0 : hyphenated.replace(/[^a-zA-Z0-9-]/g, "")) || '';
    // Convert to lowercase for URL consistency
    return (slug === null || slug === void 0 ? void 0 : slug.toLowerCase()) || '';
};
exports.default = ({ strapi }) => {
    // TODO remove this deprecated
    const updateUrlExisting = async (simplePage) => {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q;
        try {
            // TODO remove this function as it is deprecated
            if (simplePage) {
                return;
            }
            if (!(simplePage === null || simplePage === void 0 ? void 0 : simplePage.type.includes('Country'))) {
                return;
            }
            const page = await ((_a = strapi.entityService) === null || _a === void 0 ? void 0 : _a.findOne('api::page.page', simplePage.id, { populate: ['country'] }));
            // console.log('updateUrlExisting page: ', JSON.stringify(page, null, 2));
            console.log('updateUrlExisting page:', (page === null || page === void 0 ? void 0 : page.title) || 'no page');
            if (!page) {
                console.log('updateUrlExisting error: no page found');
                return;
            }
            let prefixURL = page.url;
            console.log('updateUrlExisting prefixURL:', prefixURL || 'no url');
            console.log('updateUrlExisting page.type:', page.type || 'no page.type');
            console.log('updateUrlExisting page.locale:', page.locale || 'no page.locale');
            // console.log('updateUrlExisting page.country: ', page.country);
            const isCustom = (_b = page.type) === null || _b === void 0 ? void 0 : _b.includes('Custom');
            console.log('updateUrlExisting isCustom: ', isCustom);
            const pageType = page.type.replace('Custom', '');
            console.log('updateUrlExisting pageType: ', pageType);
            const language = page.locale;
            console.log('updateUrlExisting language: ', language);
            if (isCustom) {
                let country = (_c = page.country) === null || _c === void 0 ? void 0 : _c[0];
                if (country && country.locale !== language) {
                    const countriesFound = await ((_d = strapi.entityService) === null || _d === void 0 ? void 0 : _d.findMany('api::core-country.core-country', {
                        filters: {
                            codeAlpha2: country.codeAlpha2,
                        },
                        locale: language
                    })) || [];
                    country = countriesFound[0];
                }
                if (!country) {
                    console.log('updateUrlExisting error: no country found');
                    return;
                }
                // console.log('updateUrlExisting country: ', country);
                const urlFounds = await ((_e = strapi.entityService) === null || _e === void 0 ? void 0 : _e.findMany('api::url.url', {
                    filters: {
                        pageType: pageType,
                        country: {
                            codeAlpha2: country.codeAlpha2,
                            locale: language
                        }
                    },
                    locale: language,
                    populate: ['country'],
                    limit: 1
                })) || [];
                // console.log('urlFounds: ', urlFounds);
                const urlFound = urlFounds[0];
                // console.log('urlFound: ', urlFound);
                const suffixURL = (urlFound === null || urlFound === void 0 ? void 0 : urlFound.suffixURL) || sanitizeCountryName(country.name);
                if (urlFound) {
                    await ((_f = strapi.entityService) === null || _f === void 0 ? void 0 : _f.update('api::url.url', urlFound.id, {
                        data: {
                            urlType: 'Existing',
                            prefixURL: urlFound.prefixURL,
                            suffixURL,
                        }
                    }));
                }
                else {
                    const parentsPageFound = await ((_g = strapi.entityService) === null || _g === void 0 ? void 0 : _g.findMany('api::page.page', {
                        filters: {
                            type: pageType,
                        },
                        locale: language,
                        limit: 1,
                    })) || [];
                    const parentPageFound = parentsPageFound[0];
                    if (!parentPageFound) {
                        console.log(`updateUrlExisting error: no ${pageType} page found`);
                        return;
                    }
                    prefixURL = parentPageFound.url;
                    await ((_h = strapi.entityService) === null || _h === void 0 ? void 0 : _h.create('api::url.url', {
                        data: {
                            urlType: 'Existing',
                            pageType: pageType,
                            locale: language,
                            prefixURL,
                            suffixURL,
                            country: { set: [{ id: country.id }] },
                        }
                    }));
                }
            }
            else {
                const urlFounds = await ((_j = strapi.entityService) === null || _j === void 0 ? void 0 : _j.findMany('api::url.url', {
                    filters: {
                        pageType: pageType,
                    },
                    locale: language,
                    populate: ['country'],
                    limit: 300,
                }));
                // console.log('urlsFound:', JSON.stringify(urlFounds, null, 2));
                const customPageCountries = await ((_k = strapi.entityService) === null || _k === void 0 ? void 0 : _k.findMany('api::page.page', {
                    filters: {
                        type: `${pageType}Custom`,
                    },
                    locale: language,
                    populate: ['country']
                }));
                const customCountryIds = customPageCountries === null || customPageCountries === void 0 ? void 0 : customPageCountries.map((p) => { var _a, _b; return (_b = (_a = p.country) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.codeAlpha2; });
                let filters = {
                    ...GenericPageTypes[pageType]
                };
                // TODO update to replace remittanceActive, mobileTopUpActive, giftCardActive by countryServiceType.active
                const activeServiceCountries = await ((_l = strapi.entityService) === null || _l === void 0 ? void 0 : _l.findMany('api::core-country.core-country', {
                    filters,
                    locale: language
                }));
                // console.log('updateUrlExisting activeServiceCountries: ', JSON.stringify(activeServiceCountries, null, 2));
                const leftCountries = activeServiceCountries === null || activeServiceCountries === void 0 ? void 0 : activeServiceCountries.filter((c) => !customCountryIds.includes(c.codeAlpha2));
                for (const country of leftCountries) {
                    const suffixURL = sanitizeCountryName(country === null || country === void 0 ? void 0 : country.name);
                    const urlFound = urlFounds.find((url) => { var _a; return ((_a = url.country) === null || _a === void 0 ? void 0 : _a.codeAlpha2) === country.codeAlpha2; });
                    // console.log('urlFound: ', urlFound, country.id, country.name)
                    if (!urlFound) {
                        try {
                            await ((_m = strapi.entityService) === null || _m === void 0 ? void 0 : _m.create('api::url.url', {
                                data: {
                                    urlType: 'Referred',
                                    pageType: pageType,
                                    locale: language,
                                    country: { set: [{ id: country.id }] },
                                    prefixURL: prefixURL,
                                    suffixURL: suffixURL,
                                }
                            }));
                        }
                        catch (error) {
                            if ((_o = error.message) === null || _o === void 0 ? void 0 : _o.includes('This attribute must be unique')) {
                                console.info('updateUrlExisting create url duplicate');
                            }
                            else {
                                console.log('updateUrlExisting create url fail', error);
                            }
                        }
                    }
                    else {
                        await ((_p = strapi.entityService) === null || _p === void 0 ? void 0 : _p.update('api::url.url', urlFound.id, {
                            data: {
                                prefixURL: prefixURL,
                                suffixURL: suffixURL,
                                country: { set: [{ id: country.id }] }
                            }
                        }));
                    }
                }
                for (const url of urlFounds) {
                    console.log('urlfound: ', url.url);
                    let country = url.country;
                    if (!country) {
                        continue;
                    }
                    const prefixURL = page.url;
                    const suffixURL = sanitizeCountryName(country.name);
                    console.log('updating url: ', prefixURL, suffixURL);
                    await ((_q = strapi.entityService) === null || _q === void 0 ? void 0 : _q.update('api::url.url', url.id, {
                        data: {
                            prefixURL: prefixURL,
                            suffixURL: suffixURL,
                            url: `${prefixURL}-${suffixURL}`,
                            country: { set: [{ id: country.id }] }
                        }
                    }));
                }
            }
        }
        catch (error) {
            console.log('error: ', error);
        }
    };
    const getUrlsByCountry = async ({ country, activeServices = ['remittance', 'mobiletopup', 'giftcard'] }) => {
        var _a, _b;
        // console.log(`country name: ${country.name}, codealpha2:${country.codeAlpha2}`);
        const lang = country.locale;
        const isCountryActiveServicesPromise = (_a = strapi.entityService) === null || _a === void 0 ? void 0 : _a.findMany('api::country-service-type.country-service-type', {
            filters: {
                country: { codeAlpha2: country.codeAlpha2 },
                active: true
            },
            populate: ['country', 'service'],
            locale: lang,
        });
        const urlsFoundPromise = ((_b = strapi.entityService) === null || _b === void 0 ? void 0 : _b.findMany('api::url.url', {
            filters: {
                url: { $notNull: true },
                country: {
                    codeAlpha2: country.codeAlpha2,
                    locale: lang
                }
            },
            populate: ['country'],
            locale: lang,
        })) || [];
        const [isCountryActiveServices, urlsFound] = await Promise.all([
            isCountryActiveServicesPromise,
            urlsFoundPromise
        ]);
        const finalUrls = {};
        isCountryActiveServices === null || isCountryActiveServices === void 0 ? void 0 : isCountryActiveServices.forEach((countryServiceType) => {
            var _a;
            const serviceSlug = countryServiceType.service.slug || ((_a = countryServiceType.service.name) === null || _a === void 0 ? void 0 : _a.toLowerCase().trim()) || '';
            // console.log('countryServiceType: ', JSON.stringify(countryServiceType));
            const urlFound = urlsFound === null || urlsFound === void 0 ? void 0 : urlsFound.filter((url) => {
                const pageType = url.pageType.replace('Country', '').replace('Custom', '').toLowerCase();
                // console.log('url: ', activeServices, pageType, activeServices.includes(pageType));
                return activeServices.includes(pageType) || (pageType.includes('top-up') && activeServices.includes('mobiletopup'));
            }).find((url) => {
                const pageType = url.pageType.replace('Country', '').replace('Custom', '').toLowerCase();
                // console.log('url: ', serviceSlug, pageType, pageType === serviceSlug);
                return pageType === serviceSlug || (pageType.includes('top-up') && serviceSlug.includes('mobiletopup'));
            });
            if (urlFound) {
                finalUrls[`${serviceSlug}Url`] = urlFound.url;
            }
        });
        return finalUrls;
    };
    return {
        updateUrlExisting,
        getUrlsByCountry
    };
};
