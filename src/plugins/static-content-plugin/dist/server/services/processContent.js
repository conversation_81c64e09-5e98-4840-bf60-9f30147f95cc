"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const html_entities_1 = require("html-entities");
const linkedom_1 = require("linkedom");
const contentProcessor_1 = require("../utils/contentProcessor");
const AZURE_CDN_ENDPOINT_TO_KEEP = process.env.AZURE_CDN_ENDPOINT_TO_KEEP || '';
const AZURE_CDN_ENDPOINT_TO_REPLACE = process.env.AZURE_CDN_ENDPOINT_TO_REPLACE || '';
// console.log('AZURE_CDN_ENDPOINT_TO_KEEP: ', AZURE_CDN_ENDPOINT_TO_KEEP);
const UnWantedFields = [
    'id',
    'sysName',
    'locale',
    'publishedAt',
    'sitemap_exclude',
    'updatedBy',
    'createdBy',
    'createdAt',
    'updatedAt',
    'localizations',
    'pages',
    'customLayoutDynamicZone',
    'customApplyLayout',
    'customComponents',
    'layout',
];
const VerifiedTranslations = {
    'en': 'Verified Customer',
    'es': 'Cliente Verificado',
    'fr': 'Client Vérifié',
    'de': 'Verifizierter Kunde',
    'it': 'Cliente Verificato',
    'nl': 'Geverifieerde Klant',
    'pt': 'Cliente Verificado',
    'ru': 'Проверенный клиент',
};
const UrlByServiceAndLocale = {
    Remittance: {
        en: "send-money-to",
        es: "enviar-dinero-a",
        fr: "envoyer-de-largent-a",
        de: "geld-senden-nach",
        pt: "enviar-dinheiro-para"
    },
    GiftCard: {
        en: "send-digital-gift-cards",
        es: "enviar-tarjetas-regalo-digitales",
        fr: "envoyer-des-cartes-cadeaux-numériques",
        de: "stuur-digitale-cadeaubonnen-naar",
        pt: "enviar-cartões-presente-digitais"
    },
    MobileTopUp: {
        en: "send-mobile-top-up",
        es: "enviar-recarga-movil-a",
        fr: "envoyer-une-recharge-mobile-à",
        de: "stuur-mobiel-opwaarderen-naar",
        pt: "enviar-recarga-de-celular-para"
    }
};
const IMAGE_ICON_PICTURE_FIELDS = [
    'media',
    'iconImage',
    'image',
    'icon',
    'userPict',
    'logo',
    'relatedImages',
    'headerImage'
];
const NUGGETS_MAPPING = {
    'country_name': 'country'
};
const CHUNK_SIZE = 1000; // Process 1000 characters at a time
const MAX_RECURSION_DEPTH = 10; // Prevent infinite recursion
exports.default = ({ strapi }) => {
    const getCountryFromPage = (page) => {
        var _a;
        const countryfound = ((_a = page.country) === null || _a === void 0 ? void 0 : _a[0]) || null;
        return countryfound;
    };
    // TODO done (update to replace remittanceActive, mobileTopUpActive, giftCardActive by countryServiceType.active)
    const getUrlsByCountry = async (currentCountry) => {
        console.log('getUrlsByCountry ...');
        const response = await strapi.plugins['static-content-plugin'].services.manageUrls.getUrlsByCountry({ country: currentCountry });
        return response;
    };
    const processInternalNuggets = (markdowntext, originalElement) => {
        var _a, _b;
        try {
            let country = (_a = originalElement.country) === null || _a === void 0 ? void 0 : _a[0];
            const { document } = (0, linkedom_1.parseHTML)(markdowntext);
            const nuggets = document.querySelectorAll('dynamic');
            for (const nugget of nuggets) {
                const data = nugget.getAttribute('data');
                const { id = null, type = null, name = null } = data ? JSON.parse(data) : {};
                // console.log('1 id:', id, 'type:', type, 'name:', name);
                if (id && type === 'internal') {
                    switch (name) {
                        case 'country_name':
                            let newValue = (country === null || country === void 0 ? void 0 : country.name) || '';
                            nugget.replaceWith(newValue);
                            break;
                        default:
                            break;
                    }
                }
            }
            let newContent = (0, html_entities_1.decode)(document.documentElement.outerHTML);
            // console.log('newContent: ', newContent);
            newContent = newContent.replace('<div>', '');
            newContent = newContent.replace('</div>', '');
            return newContent;
        }
        catch (error) {
            const errorDetails = {
                message: error.message,
                stack: error.stack,
                ...(((_b = error.response) === null || _b === void 0 ? void 0 : _b.data) && { responseData: error.response.data }),
                name: error.name,
                code: error.code
            };
            console.error('Error details:', JSON.stringify(errorDetails, null, 2));
            // If you need to log the full error object separately
            console.error('Full error object:', error);
            throw error;
            return markdowntext;
        }
    };
    const processInternalDynamicData = ({ name, country, id }) => {
        console.log('processInternalDynamicData name: ', name, country === null || country === void 0 ? void 0 : country.name);
        switch (name) {
            case 'country_name':
                return (country === null || country === void 0 ? void 0 : country.name) || '';
            default:
                return '';
        }
    };
    const processHeadingSection = (section) => {
        console.log('processHeadingSection ...');
        return section;
    };
    const processHeroSection = async (section, originalElement) => {
        var _a;
        console.log('processHeroSection ...');
        // console.log('originalElement country:', JSON.stringify(originalElement.country, null, 2));
        // const lang = originalElement?.locale?.code || originalElement?.locale || 'en';
        if (section.calculator !== 'None') {
            let defaultCountries = [];
            if ((_a = originalElement.country) === null || _a === void 0 ? void 0 : _a.length) {
                const country = originalElement.country[0];
                defaultCountries = [{
                        'id': country.id,
                        'name': country.name,
                        'codeAlpha2': country.codeAlpha2,
                        'remittanceDefaultCountry': true,
                        'mobileTopUpDefaultCountry': true,
                        'giftCardDefaultCountry': true,
                    }];
            } /* else {
              defaultCountries = await strapi.entityService?.findMany('api::core-country.core-country', {
                filters: {
                  $or: [
                    { remittanceDefaultCountry: true },
                    { mobileTopUpDefaultCountry: true },
                    { giftCardDefaultCountry: true },
                  ]
                },
                locale: lang,
                fields: [
                  'id',
                  'name',
                  'codeAlpha2',
                  'remittanceDefaultCountry',
                  'mobileTopUpDefaultCountry',
                  'giftCardDefaultCountry'
                ],
              });
            } */
            section.defaultCountries = defaultCountries;
            // section.title = processInternalNuggets(section.title, originalElement);
        }
        return section;
    };
    const processBannerSection = (section) => {
        //console.log('BannerSection content:', JSON.stringify(section, null, 2));
        return section;
    };
    const processActionListSection = (section) => {
        //console.log('ActionListSection content:', JSON.stringify(section, null, 2));
        return section;
    };
    const processHeadingListSection = (section) => {
        //console.log('HeadingListSection content:', JSON.stringify(section, null, 2));
        return section;
    };
    const processFeatureListSection = (section) => {
        //console.log('FeatureListSection content:', JSON.stringify(section, null, 2));
        return section;
    };
    const processTextListSection = (section) => {
        //console.log('TextListSection content:', JSON.stringify(section, null, 2));
        return section;
    };
    const processCountryFlagSection = async (section, originalElement) => {
        console.log('processCountryFlagSection ...');
        const countries = [];
        for (const country of section.countries || []) {
            // console.log('processCountryFlagSection country:', country);
            if (!country.id) {
                countries.push(country);
                continue;
            }
            const urls = await getUrlsByCountry(country);
            const urlByServiceFound = Object.entries(urls).find(([serviceName]) => {
                var _a;
                const originalPageType = originalElement.type.replace('Country', '').replace('Custom', '').toLowerCase();
                const sesionServiceType = (_a = section === null || section === void 0 ? void 0 : section.service) === null || _a === void 0 ? void 0 : _a.replace('Country', '').replace('Custom', '').toLowerCase();
                if (serviceName.includes(originalPageType)) {
                    return true;
                }
                else if (serviceName.includes(sesionServiceType)) {
                    return true;
                }
                return false;
            });
            console.log('processCountryFlagSection urlByServiceFound:', urlByServiceFound);
            const newCountry = {
                name: country.name,
                codeAlpha2: country.codeAlpha2,
                serviceUrl: urlByServiceFound === null || urlByServiceFound === void 0 ? void 0 : urlByServiceFound[1],
            };
            countries.push(newCountry);
        }
        section.countries = countries;
        return section;
    };
    const processFakeMsgSection = (section) => {
        //console.log('FakeMsgSection content:', JSON.stringify(section, null, 2));
        return section;
    };
    const processFaqListSection = async (section, originalElement) => {
        var _a, _b, _c, _d;
        //console.log('FaqSection content:', JSON.stringify(section, null, 2));
        let faqs = [];
        if ((_a = section.faqs) === null || _a === void 0 ? void 0 : _a.length) {
            faqs = section.faqs;
        }
        else {
            const lang = originalElement.locale || 'en';
            const countryId = (_c = (_b = originalElement.country) === null || _b === void 0 ? void 0 : _b[0]) === null || _c === void 0 ? void 0 : _c.id;
            const pageType = (originalElement.type || 'None').replace('Country', '').replace('Custom', '');
            const filters = {};
            if (countryId) {
                filters.countries = { $in: [countryId] };
            }
            if (pageType !== 'None') {
                filters.type = { $eq: pageType };
            }
            faqs = await ((_d = strapi.entityService) === null || _d === void 0 ? void 0 : _d.findMany('api::faq.faq', {
                filters,
                limit: 5,
                locale: lang,
                fields: ['title', 'description']
            }));
        }
        section.faqs = faqs.map(({ title, description }) => ({ title, description }));
        return section;
    };
    const processReviewSection = async (section, originalElement) => {
        var _a, _b;
        //console.log('processReviewSection content:', JSON.stringify(section, null, 2));
        const type = section.type;
        const verified = type === 'real';
        const reviews = await ((_a = strapi.entityService) === null || _a === void 0 ? void 0 : _a.findMany('api::global-review.global-review', {
            filters: {
                verified: verified
            },
            limit: 5,
        }));
        const lang = ((_b = originalElement === null || originalElement === void 0 ? void 0 : originalElement.locale) === null || _b === void 0 ? void 0 : _b.code) || (originalElement === null || originalElement === void 0 ? void 0 : originalElement.locale) || 'en';
        //console.log('processReviewSection reviews:', reviews);
        section.reviews = reviews.map((review) => {
            return {
                ...review,
                verifiedCustomer: verified ? VerifiedTranslations[lang] || 'Verified Customer' : '',
            };
        });
        return section;
    };
    const processCtaSection = (section) => {
        //console.log('CtaSection content:', JSON.stringify(section, null, 2));
        return section;
    };
    const processLogoListSection = (section) => {
        //console.log('LogoListSection content:', JSON.stringify(section, null, 2));
        return section;
    };
    const processCustomSection = (section) => {
        //console.log('CustomSection content:', JSON.stringify(section, null, 2));
        const customComponents = section.customComponents || [];
        const newCustomComponents = [];
        customComponents.forEach((component) => {
            if (Array.isArray(component.content)) {
                newCustomComponents.push(...component.content);
            }
        });
        //console.log('CustomSection newCustomComponents:', JSON.stringify(newCustomComponents, null, 2));
        return newCustomComponents;
    };
    const processBlogsByCategorySection = async (section, originalElement) => {
        var _a, _b, _c;
        console.log('processBlogsByCategorySection ... ');
        const lang = ((_a = originalElement === null || originalElement === void 0 ? void 0 : originalElement.locale) === null || _a === void 0 ? void 0 : _a.code) || (originalElement === null || originalElement === void 0 ? void 0 : originalElement.locale) || 'en';
        // console.log('processBlogsByCategorySection locale: ', lang)
        const allCategories = await ((_b = strapi.entityService) === null || _b === void 0 ? void 0 : _b.findMany('api::post-category.post-category', {
            locale: lang,
        }));
        //console.log('allCategories:', JSON.stringify(allCategories, null, 2))
        const categoryList = [];
        const categoryIds = [];
        allCategories === null || allCategories === void 0 ? void 0 : allCategories.forEach((category) => {
            //console.log('category:', JSON.stringify(category, null, 2))
            categoryList.push({
                categoryId: category.id,
                name: category.name,
                blogs: []
            });
            categoryIds.push(category.id);
        });
        // console.log('categoryIds: ', categoryIds);
        let blogs = await ((_c = strapi.entityService) === null || _c === void 0 ? void 0 : _c.findMany('api::news-post.news-post', {
            filters: {
                categories: { $in: categoryIds },
                publishedAt: { $notNull: true }
            },
            populate: ['seo', 'headerImage', 'categories'],
            locale: lang,
        })) || [];
        // console.log('1 blogs: ', JSON.stringify(blogs, null, 2));
        blogs = blogs.map(({ content, localizations, categories, id: blogId, ...rest }) => {
            console.log('processBlogsByCategorySection blog', blogId, rest.url);
            // console.log('processBlogsByCategorySection blog.categories', categories);
            categories === null || categories === void 0 ? void 0 : categories.forEach(({ id }) => {
                var _a;
                const categoryIndex = categoryList.findIndex(({ categoryId }) => categoryId === id);
                if (categoryIndex > -1) {
                    (_a = categoryList[categoryIndex]) === null || _a === void 0 ? void 0 : _a.blogs.push(blogId);
                }
            });
            return {
                ...rest,
                date: rest.publishedAt,
                id: blogId,
                categories: (categories === null || categories === void 0 ? void 0 : categories.map(({ id }) => {
                    // console.log('processBlogsByCategorySection blog.categories.id', id);
                    return id;
                })) || [],
            };
        });
        // console.log('1 processBlogsByCategorySection blogs: ', JSON.stringify(blogs, null, 2));
        // console.log('processBlogsByCategorySection categoryList:', JSON.stringify(categoryList, null, 2));
        section.blogByCategories = {
            blogs: blogs.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()),
            categories: categoryList.filter(({ blogs }) => blogs === null || blogs === void 0 ? void 0 : blogs.length),
        };
        return section;
    };
    const processCountrySelectorSection = async (section, originalElement) => {
        /* const lang = originalElement?.locale?.code || originalElement?.locale || 'en';
        const countries = await strapi.entityService?.findMany('api::core-country.core-country', {
          filters: {
            locale: lang,
          },
          limit: 500
        });
    
        const url = originalElement.url || `https://sendvalu.com/${lang}`;
        const urlsplit = url.split(`/${lang}`);
        const rootUrl = urlsplit[0];
        section.countryList = countries?.map((country) => {
          return {
            ...country,
            urls: {
              Remittance: `${rootUrl}/${lang}/${UrlByServiceAndLocale.Remittance[lang]}-${country.name.toLowerCase().split(' ').join('-')}`,
            }
          }
        }); */
        return section;
    };
    const processAllCountrySearchSection = async (section, originalElement) => {
        var _a, _b;
        console.log('processAllCountrySearchSection ...');
        const lang = ((_a = originalElement === null || originalElement === void 0 ? void 0 : originalElement.locale) === null || _a === void 0 ? void 0 : _a.code) || (originalElement === null || originalElement === void 0 ? void 0 : originalElement.locale) || 'en';
        const countries = await ((_b = strapi.entityService) === null || _b === void 0 ? void 0 : _b.findMany('api::core-country.core-country', {
            locale: lang,
            limit: 500
        }));
        section.countryList = await Promise.all(countries === null || countries === void 0 ? void 0 : countries.map(async (country) => {
            return {
                ...country,
                urls: {
                    ...(await getUrlsByCountry(country))
                }
            };
        }));
        section.services = ['Remittance', 'MobileTopUp', 'GiftCard'];
        return section;
    };
    const processFAQsByCategoriesSection = async (section, originalElement) => {
        var _a, _b;
        const lang = ((_a = originalElement === null || originalElement === void 0 ? void 0 : originalElement.locale) === null || _a === void 0 ? void 0 : _a.code) || (originalElement === null || originalElement === void 0 ? void 0 : originalElement.locale) || 'en';
        const allCategories = await ((_b = strapi.entityService) === null || _b === void 0 ? void 0 : _b.findMany('api::category.category', {
            populate: ['deep'],
            locale: lang
        }));
        const categories = [];
        const faqByCategories = {};
        allCategories === null || allCategories === void 0 ? void 0 : allCategories.forEach((category) => {
            var _a;
            // console.log('category:', JSON.stringify(category, null, 2))
            categories.push({
                categoryId: category.id,
                name: category.name,
            });
            faqByCategories[category.id] = (_a = category.faqs) === null || _a === void 0 ? void 0 : _a.map(({ categories, countries, ...rest }) => ({
                ...rest,
                category: {
                    name: category.name,
                    categoryId: category.id
                }
            }));
        });
        section.faqByCategories = {
            faqByCategories,
            categories,
        };
        return section;
    };
    const processContactUsFormSection = async (section, originalElement) => {
        var _a, _b;
        const lang = ((_a = originalElement === null || originalElement === void 0 ? void 0 : originalElement.locale) === null || _a === void 0 ? void 0 : _a.code) || (originalElement === null || originalElement === void 0 ? void 0 : originalElement.locale) || 'en';
        const allCategories = await ((_b = strapi.entityService) === null || _b === void 0 ? void 0 : _b.findMany('api::category.category', {
            populate: ['deep'],
            locale: lang
        }));
        const categories = [];
        const faqByCategories = {};
        allCategories === null || allCategories === void 0 ? void 0 : allCategories.forEach((category) => {
            var _a;
            // console.log('category:', JSON.stringify(category, null, 2))
            categories.push({
                categoryId: category.id,
                name: category.name,
            });
            faqByCategories[category.id] = (_a = category.faqs) === null || _a === void 0 ? void 0 : _a.map(({ categories, countries, ...rest }) => ({
                ...rest,
                category: {
                    name: category.name,
                    categoryId: category.id
                }
            }));
        });
        section.faqByCategories = {
            faqByCategories,
            categories,
        };
        return section;
    };
    const processShopListByCategorySection = async (section, originalElement) => {
        var _a, _b;
        const lang = ((_a = originalElement === null || originalElement === void 0 ? void 0 : originalElement.locale) === null || _a === void 0 ? void 0 : _a.code) || (originalElement === null || originalElement === void 0 ? void 0 : originalElement.locale) || 'en';
        const shopsCount = section.shopsCount || 3;
        const country = getCountryFromPage(originalElement);
        const allCategories = await ((_b = strapi.entityService) === null || _b === void 0 ? void 0 : _b.findMany('api::shop-category.shop-category', {
            populate: ['deep'],
            locale: lang
        }));
        const categories = [];
        const shopsByCategories = {};
        allCategories === null || allCategories === void 0 ? void 0 : allCategories.forEach((category) => {
            var _a;
            // console.log('category:', JSON.stringify(category, null, 2))
            categories.push({
                categoryId: category.id,
                name: category.name,
            });
            const uniqueShops = {};
            (_a = category.shops) === null || _a === void 0 ? void 0 : _a.filter((shop) => {
                let filterByCountry = true;
                if (country === null || country === void 0 ? void 0 : country.length) {
                    filterByCountry = shop.country.map(({ codeAlpha2 }) => codeAlpha2).includes(country.codeAlpha2);
                }
                return filterByCountry;
            }).slice(0, shopsCount).forEach(({ id, category, country, partner, ...rest }) => {
                uniqueShops[id] = {
                    ...rest,
                    partner: {
                        name: partner === null || partner === void 0 ? void 0 : partner.name,
                        logo: partner === null || partner === void 0 ? void 0 : partner.logo,
                    },
                    country: {
                        name: country === null || country === void 0 ? void 0 : country.name,
                        codeAlpha2: country === null || country === void 0 ? void 0 : country.codeAlpha2
                    },
                    category: {
                        name: category.name,
                        categoryId: category.id
                    }
                };
            });
            shopsByCategories[category.id] = Object.values(uniqueShops);
        });
        section.shopsByCategories = {
            shopsByCategories,
            categories,
        };
        return section;
    };
    const processShopListSection = async (section, originalElement) => {
        var _a, _b, _c;
        console.log('processShopListSection ...');
        const lang = ((_a = originalElement === null || originalElement === void 0 ? void 0 : originalElement.locale) === null || _a === void 0 ? void 0 : _a.code) || (originalElement === null || originalElement === void 0 ? void 0 : originalElement.locale) || 'en';
        const shopsCount = section.shopsCount || 3;
        const originalCountry = getCountryFromPage(originalElement);
        // console.log('processShopListSection originalCountry', JSON.stringify(originalCountry, null, 2));
        // console.log('processShopListSection section.shops', section.shops);
        let shops = section.shops || [];
        if (!(shops === null || shops === void 0 ? void 0 : shops.length)) {
            shops = await ((_b = strapi.entityService) === null || _b === void 0 ? void 0 : _b.findMany('api::shop.shop', {
                populate: ['country', 'logo'],
                locale: lang
            })) || [];
        }
        // console.log('processShopListSection 1 shops:', JSON.stringify(shops, null, 2));
        let shopsByCountry = shops;
        if (originalCountry) {
            shopsByCountry = (shops === null || shops === void 0 ? void 0 : shops.filter(({ country }) => {
                // console.log('processShopListSection shop country:', JSON.stringify(country, null, 2));
                return country.find(({ codeAlpha2 }) => codeAlpha2 === originalCountry.codeAlpha2);
            })) || [];
        }
        if (!((_c = section.shops) === null || _c === void 0 ? void 0 : _c.length) && shopsByCountry.length > shopsCount) {
            shopsByCountry = shopsByCountry.slice(0, shopsCount);
        }
        section.shops = shopsByCountry.map(({ logo, name }) => ({ logo, name }));
        return section;
    };
    const processPromotionListSection = async (section, originalElement) => {
        var _a, _b;
        console.log('processPromotionListSection ...');
        const lang = ((_a = originalElement === null || originalElement === void 0 ? void 0 : originalElement.locale) === null || _a === void 0 ? void 0 : _a.code) || (originalElement === null || originalElement === void 0 ? void 0 : originalElement.locale) || 'en';
        const whichShow = section.whichShow;
        let promotions = section.promotions || [];
        const count = section.promosCount || 3;
        switch (whichShow) {
            case 'Most recents':
                promotions = await ((_b = strapi.entityService) === null || _b === void 0 ? void 0 : _b.findMany('api::promotion.promotion', {
                    filters: {
                        sort: {
                            createdAt: 'asc'
                        }
                    },
                    locale: lang,
                    limit: count,
                    populate: ["deep"]
                }));
                break;
            case 'Selected':
                break;
            default:
                break;
        }
        section.promotions = promotions;
        return section;
    };
    const processNewsListSection = async (section, originalElement) => {
        var _a, _b, _c;
        console.log('processNewsListSection ...');
        if ((_a = section.news) === null || _a === void 0 ? void 0 : _a.length) {
            return section;
        }
        const lang = ((_b = originalElement === null || originalElement === void 0 ? void 0 : originalElement.locale) === null || _b === void 0 ? void 0 : _b.code) || (originalElement === null || originalElement === void 0 ? void 0 : originalElement.locale) || 'en';
        const news = await ((_c = strapi.entityService) === null || _c === void 0 ? void 0 : _c.findMany('api::news-post.news-post', {
            filters: {
                publishedAt: { $notNull: true }
            },
            locale: lang,
            limit: section.newsCount,
            populate: ['headerImage', 'categories']
        }));
        section.news = news === null || news === void 0 ? void 0 : news.map(({ title, summary, categories, url, headerImage, publishedAt }) => ({
            date: publishedAt,
            title,
            summary,
            categories: (categories === null || categories === void 0 ? void 0 : categories.map(({ name }) => name)) || [],
            url,
            headerImage,
        })).sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
        return section;
    };
    const processPartnersListSection = async (section, originalElement) => {
        var _a;
        console.log('processPartnersListSection ...');
        const originalCountry = getCountryFromPage(originalElement);
        // console.log('processPartnersListSection country: ', JSON.stringify(country, null, 2));
        // const lang = originalElement?.locale?.code || originalElement?.locale || 'en';
        const filters = {};
        const partners = {};
        const distributors = await ((_a = strapi.entityService) === null || _a === void 0 ? void 0 : _a.findMany('api::distributor.distributor', {
            filters,
            populate: ['deep']
        })) || [];
        // console.log('processPartnersListSection distributors', JSON.stringify(distributors, null, 2));
        distributors
            .filter((p) => { var _a; return !!((_a = p === null || p === void 0 ? void 0 : p.partner) === null || _a === void 0 ? void 0 : _a.partner); })
            .filter((p) => { var _a; return originalCountry ? ((_a = p === null || p === void 0 ? void 0 : p.country) === null || _a === void 0 ? void 0 : _a.codeAlpha2) === originalCountry.codeAlpha2 : true; })
            .forEach(({ partner }) => {
            // console.log('partnerlist partner: ', JSON.stringify(partner, null, 2))
            const { identifier, name, logo } = partner.partner;
            partners[identifier] = {
                name,
                logo
            };
        });
        /* const telcoms = await strapi.entityService?.findMany('api::telcom.telcom', {
          filters,
          populate: ['deep']
        }) || [];
        partners.push(...telcoms.map(({ partner }) => partner));
        const shops = await strapi.entityService?.findMany('api::shop.shop', {
          filters,
          populate: ['deep']
        }) || [];
        partners.push(...shops.map(({ partner }) => partner)); */
        section.partners = Object.values(partners);
        return section;
    };
    const processFeatureListAccordionSection = (section) => {
        console.log('processFeatureListAccordionSection ...');
        const countries = section.countries || [];
        section.countries = countries.map((country) => {
            return {
                name: country.name,
                codeAlpha2: country.codeAlpha2
            };
        });
        return section;
    };
    const processOperatorsListSection = async (section, originalElement) => {
        var _a, _b;
        console.log('processOperatorsListSection ...');
        const lang = originalElement.locale || 'en';
        const telcomsCount = section.telcomsCount || 3;
        const originalCountry = getCountryFromPage(originalElement);
        const countryId = originalCountry === null || originalCountry === void 0 ? void 0 : originalCountry.id;
        // console.log('processOperatorsListSection originalCountry:', JSON.stringify(originalCountry, null, 2));
        // console.log('processOperatorsListSection section.telcoms', section.telcoms);
        let telcoms = section.telcoms || [];
        if (!(telcoms === null || telcoms === void 0 ? void 0 : telcoms.length)) {
            const filters = {};
            if (countryId) {
                filters.countries = { $in: [countryId] };
            }
            telcoms = await ((_a = strapi.entityService) === null || _a === void 0 ? void 0 : _a.findMany('api::telcom.telcom', {
                filters,
                populate: ['logo', 'countries'],
                locale: lang,
                limit: telcomsCount
            }));
        }
        //console.log('processOperatorsListSection telcoms:', JSON.stringify(telcoms, null, 2));
        let telcomsByCountry = telcoms;
        if (originalCountry) {
            telcomsByCountry = (telcoms === null || telcoms === void 0 ? void 0 : telcoms.filter(({ countries }) => {
                return countries.find(({ codeAlpha2 }) => codeAlpha2 === originalCountry.codeAlpha2);
            })) || [];
        }
        if (!((_b = section.telcoms) === null || _b === void 0 ? void 0 : _b.length) && telcomsByCountry.length > telcomsCount) {
            telcomsByCountry = telcomsByCountry.slice(0, telcomsCount);
        }
        section.telcoms = (telcomsByCountry === null || telcomsByCountry === void 0 ? void 0 : telcomsByCountry.map(({ name, logo }) => ({ name, logo }))) || [];
        return section;
    };
    const processArticleSection = (section) => {
        console.log('processArticleSection ...');
        const article = section.article;
        if (article) {
            const category = article === null || article === void 0 ? void 0 : article.cetagory;
            section.article = {
                ...article,
                category: category
                    ? {
                        name: category.name
                    }
                    : {}
            };
        }
        return section;
    };
    const processDistributorsListSection = async (section, originalElement) => {
        var _a, _b, _c, _d, _e, _f;
        console.log('processDistributorsListSection ...');
        if ((_a = section.distributors) === null || _a === void 0 ? void 0 : _a.length) {
            return section;
        }
        const lang = originalElement.locale;
        const originalCountry = getCountryFromPage(originalElement);
        // console.log('processDistributorsListSection originalCountry:', JSON.stringify(originalCountry, null, 2));
        const filters = {};
        const deliveryMethodList = {};
        const distributors = await ((_b = strapi.entityService) === null || _b === void 0 ? void 0 : _b.findMany('api::distributor.distributor', {
            filters,
            populate: ['deep']
        })) || [];
        // Process each distributor
        for (const distributor of distributors) {
            if (!((_c = distributor === null || distributor === void 0 ? void 0 : distributor.partner) === null || _c === void 0 ? void 0 : _c.partner)) {
                continue;
            }
            if (originalCountry && ((_d = distributor === null || distributor === void 0 ? void 0 : distributor.country) === null || _d === void 0 ? void 0 : _d.codeAlpha2) !== originalCountry.codeAlpha2) {
                continue;
            }
            ///console.log('processDistributorsListSection distributor.country:', JSON.stringify(distributor.country, null, 2));
            const { name: distributorName, deliveryMethods, logo } = distributor;
            // Process each delivery method
            for (const method of deliveryMethods) {
                // console.log('processDistributorsListSection method:', JSON.stringify(method, null, 2));
                // Find localized version of the delivery method
                let localizedMethod = (_e = method.delivery_methods_localizeds) === null || _e === void 0 ? void 0 : _e.find((loc) => loc.locale === lang);
                if (!localizedMethod) {
                    (_f = method.delivery_methods_localizeds) === null || _f === void 0 ? void 0 : _f.forEach((loc) => {
                        var _a;
                        localizedMethod = localizedMethod || ((_a = loc.localizations) === null || _a === void 0 ? void 0 : _a.find((l) => l.locale === lang));
                    });
                }
                if (localizedMethod) {
                    const { name: deliveryMethodName, description } = localizedMethod;
                    // Initialize delivery method if not exists
                    if (!deliveryMethodList[deliveryMethodName]) {
                        deliveryMethodList[deliveryMethodName] = {
                            name: deliveryMethodName,
                            description,
                            distributors: []
                        };
                    }
                    deliveryMethodList[deliveryMethodName].distributors.push({
                        name: distributorName,
                        logo
                    });
                }
            }
        }
        // Convert deliveryMethodList object to array
        section.deliveryMethods = Object.values(deliveryMethodList);
        return section;
    };
    const findDynamicData = (markdowntext) => {
        // Use the utility function to find dynamic data IDs
        return (0, contentProcessor_1.findDynamicDataIds)(markdowntext);
    };
    const detectDynamicDataElements = async (element, dynamicdataFound = []) => {
        console.log('detectDynamicDataElements element:', element.id);
        const stack = [{ element }];
        while (stack.length > 0) {
            const { element } = stack.pop();
            for (const field in element) {
                if (!UnWantedFields.includes(field)) {
                    if (field === 'content') {
                        // element.content = await processDynamicZoneContent(element[field], {});
                    }
                    if (typeof element[field] === 'string' && element[field].includes('<dynamic')) {
                        // Use the utility function to find dynamic data IDs
                        const ids = (0, contentProcessor_1.findDynamicDataIds)(element[field]);
                        dynamicdataFound.push(...ids);
                    }
                    else if (Array.isArray(element[field])) {
                        for (const object of element[field]) {
                            stack.push({ element: object });
                        }
                    }
                    else if (!!element[field] && typeof element[field] === 'object') {
                        stack.push({ element: element[field] });
                    }
                }
            }
        }
        return dynamicdataFound;
    };
    const processDynamicZoneComponent = async (component, originalElement) => {
        const newComponent = { ...component };
        switch (newComponent.__component) {
            case 'sections.heading':
                return processHeadingSection(newComponent);
            case 'sections.hero':
                return processHeroSection(newComponent, originalElement);
            case 'sections.banner':
                return processBannerSection(newComponent);
            case 'sections.action-list':
                return processActionListSection(newComponent);
            case 'sections.heading-list':
                return processHeadingListSection(newComponent);
            case 'sections.feature-list':
                return processFeatureListSection(newComponent);
            case 'sections.text-list':
                return processTextListSection(newComponent);
            case 'sections.country-flag':
                return processCountryFlagSection(newComponent, originalElement);
            case 'sections.fake-msg':
                return processFakeMsgSection(newComponent);
            case 'sections.faq-list':
                return processFaqListSection(newComponent, originalElement);
            case 'sections.review':
                return processReviewSection(newComponent, originalElement);
            case 'sections.cta':
                return processCtaSection(newComponent);
            case 'sections.logo-list':
                return processLogoListSection(newComponent);
            case 'sections.custom':
                return processCustomSection(newComponent);
            case 'sections.blogs-by-category':
                return processBlogsByCategorySection(newComponent, originalElement);
            case 'sections.all-country-search':
                return processAllCountrySearchSection(newComponent, originalElement);
            case 'sections.country-selector':
                return processCountrySelectorSection(newComponent, originalElement);
            case 'sections.faq-by-categories':
                return processFAQsByCategoriesSection(newComponent, originalElement);
            case 'sections.contact-us-form':
                return processContactUsFormSection(newComponent, originalElement);
            case 'sections.shop-list':
                return processShopListSection(newComponent, originalElement);
            case 'sections.promotions-list':
                return processPromotionListSection(newComponent, originalElement);
            case 'sections.news-list':
                return processNewsListSection(newComponent, originalElement);
            case 'sections.partners-list':
                return processPartnersListSection(newComponent, originalElement);
            case 'sections.feat-list-acordion':
                return processFeatureListAccordionSection(newComponent);
            case 'sections.operators-list':
                return processOperatorsListSection(newComponent, originalElement);
            case 'sections.article':
                return processArticleSection(newComponent);
            case 'sections.distributors-list':
                return processDistributorsListSection(newComponent, originalElement);
            default:
                return newComponent;
        }
    };
    const processDynamicZoneContent = async (content, originalElement) => {
        console.log('processDynamicZoneContent content:', content.length, originalElement.title);
        const newContent = [];
        for (const component of content) {
            //console.log('processDynamicZoneContent component:', JSON.stringify(component, null, 2));
            const newComponent = await processDynamicZoneComponent(component, originalElement);
            //console.log('processDynamicZoneContent newComponent:', JSON.stringify(newComponent, null, 2));
            if (Array.isArray(newComponent)) {
                const newCustomContent = await processDynamicZoneContent(newComponent, originalElement);
                newContent.push(...newCustomContent);
            }
            else {
                newContent.push(newComponent);
            }
        }
        return newContent;
    };
    const processMediaField = async (field, originalElement) => {
        if (!field || !field.id) {
            return field;
        }
        console.log('processMediaField field:', field);
        const lang = originalElement.locale;
        const pageId = originalElement.id;
        const customImageServices = strapi
            .plugin('strapi-custom-images-plugin')
            .service('customImageServices');
        const customImageFound = await customImageServices.getLocalizedDataByImageId({
            imageId: field.id,
            locale: lang,
            pageId,
        });
        console.log('processMediaField customImageFound: ', customImageFound);
        if (customImageFound) {
            return {
                url: field.url,
                alternativeText: customImageFound.alt,
                caption: customImageFound.caption,
            };
        }
        return {
            url: field.url,
            name: field.name,
            alternativeText: field.alternativeText,
            caption: field.caption,
        };
    };
    const processMediaFields = async (fields, originalElement) => {
        // console.log('processMediaFields fields: ', JSON.stringify(fields, null, 2));
        let newFields = [];
        if (Array.isArray(fields)) {
            for (const field of fields) {
                const processedField = await processMediaField(field, originalElement);
                newFields.push(processedField);
            }
        }
        else {
            newFields = await processMediaField(fields, originalElement);
        }
        return newFields;
    };
    const createOrUpdateStaticContent = async ({ id, query }) => {
        var _a, _b, _c, _d, _e;
        console.log('createOrUpdateStaticContent...');
        //TODO find locale using i18n collection
        for (const locale of ['en', 'es', 'fr', 'de', 'pt', 'ru', 'it', 'nl']) {
            const findElementQuery = {
                filters: {
                    $or: [
                        {
                            url: query.data.url,
                        },
                        {
                            identifier: (_a = query.data) === null || _a === void 0 ? void 0 : _a.identifier
                        }
                    ]
                },
                locale: locale,
                fields: ['id', 'url', 'identifier'],
            };
            // console.log('createOrUpdateStaticContent findElementQuery:', JSON.stringify(findElementQuery, null, 2));
            const elementsFound = await ((_b = strapi.entityService) === null || _b === void 0 ? void 0 : _b.findMany('api::static-content.static-content', findElementQuery)) || [];
            console.log('createOrUpdateStaticContent elementsFound by locale:', elementsFound === null || elementsFound === void 0 ? void 0 : elementsFound.length, locale);
            for (const element of elementsFound) {
                //console.log('createOrUpdateStaticContent element:', element?.id, element?.identifier, element?.url);
                if (element === null || element === void 0 ? void 0 : element.id) {
                    await ((_c = strapi.entityService) === null || _c === void 0 ? void 0 : _c.delete('api::static-content.static-content', element.id));
                }
            }
        }
        try {
            return await ((_d = strapi.entityService) === null || _d === void 0 ? void 0 : _d.create('api::static-content.static-content', query));
        }
        catch (error) {
            const errorDetails = {
                message: error.message,
                stack: error.stack,
                ...(((_e = error.response) === null || _e === void 0 ? void 0 : _e.data) && { responseData: error.response.data }),
                name: error.name,
                code: error.code
            };
            console.error('Error details:', JSON.stringify(errorDetails, null, 2));
            // If you need to log the full error object separately
            console.error('Full error object:', error);
            throw error;
        }
    };
    const processUrl = async (urlComponent, originalElement) => {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        const { url: globalUrl, localizations, country: countries, type: pageType, locale } = originalElement;
        const pageCountry = countries[0];
        console.log('processUrl original page type: ', pageType);
        let finalUrl = urlComponent;
        if ((_a = urlComponent === null || urlComponent === void 0 ? void 0 : urlComponent.url) === null || _a === void 0 ? void 0 : _a.urlType) {
            finalUrl = (_b = urlComponent === null || urlComponent === void 0 ? void 0 : urlComponent.url) === null || _b === void 0 ? void 0 : _b.url;
            const { urlType, page, referredUrl, externalUrl, lang } = urlComponent === null || urlComponent === void 0 ? void 0 : urlComponent.url;
            console.log('processUrl urlType:', urlType, ' page:', page, ' lang:', lang);
            if (urlType === 'Automatically generated') {
                if (!(pageType === null || pageType === void 0 ? void 0 : pageType.includes('Country'))) {
                    const pageFound = await ((_c = strapi.entityService) === null || _c === void 0 ? void 0 : _c.findOne('api::page.page', originalElement.id, {
                        populate: ['localizations']
                    }));
                    // console.log('pageFound: ', JSON.stringify(pageFound, null, 2));
                    if (pageFound) {
                        if (pageFound.locale === lang) {
                            finalUrl = pageFound.url;
                        }
                        else {
                            const localizationFound = pageFound.localizations.find((l) => l.locale === lang);
                            if (localizationFound) {
                                finalUrl = localizationFound.url;
                            }
                        }
                    }
                }
                else {
                    const filters = {
                        pageType: pageType === null || pageType === void 0 ? void 0 : pageType.replace('Custom', ''),
                        country: {
                            codeAlpha2: pageCountry === null || pageCountry === void 0 ? void 0 : pageCountry.codeAlpha2,
                            locale: lang
                        }
                    };
                    console.log('filters: ', JSON.stringify(filters, null, 2));
                    const urlsFound = await ((_d = strapi.entityService) === null || _d === void 0 ? void 0 : _d.findMany('api::url.url', {
                        filters: filters,
                        populate: ['country'],
                        locale: lang,
                    })) || [];
                    console.log('urlsFound: ', JSON.stringify(urlsFound, null, 2));
                    const urlFound = urlsFound === null || urlsFound === void 0 ? void 0 : urlsFound.find(({ country }) => (country === null || country === void 0 ? void 0 : country.codeAlpha2) === (pageCountry === null || pageCountry === void 0 ? void 0 : pageCountry.codeAlpha2));
                    console.log('urlFound: ', JSON.stringify(urlFound, null, 2));
                    finalUrl = (urlFound === null || urlFound === void 0 ? void 0 : urlFound.url) || ``;
                }
            }
            else if (urlType === 'Existing page') {
                const pageFound = await ((_e = strapi.entityService) === null || _e === void 0 ? void 0 : _e.findOne('api::page.page', page, {
                    populate: ['localizations']
                }));
                if (pageFound) {
                    if (pageFound.locale === locale) {
                        finalUrl = pageFound.url;
                    }
                    else {
                        const localizationFound = pageFound.localizations.find((l) => l.locale === locale);
                        if (localizationFound) {
                            finalUrl = localizationFound.url;
                        }
                    }
                }
            }
            else if (urlType === 'Referred page') {
                const urlFound = await ((_f = strapi.entityService) === null || _f === void 0 ? void 0 : _f.findOne('api::url.url', referredUrl, {
                    populate: ['localizations', 'country']
                }));
                if (urlFound) {
                    if (urlFound.locale === locale) {
                        finalUrl = urlFound.url;
                    }
                    else {
                        let urlByLocale = (_g = urlFound.localizations) === null || _g === void 0 ? void 0 : _g.find((l) => l.locale === locale);
                        if (urlByLocale) {
                            finalUrl = urlByLocale.url;
                        }
                        else {
                            const urlCountry = urlFound.country;
                            const urlByLocaleResponse = await ((_h = strapi.entityService) === null || _h === void 0 ? void 0 : _h.findMany('api::url.url', {
                                filters: {
                                    pageType: urlFound.pageType,
                                    country: {
                                        codeAlpha2: urlCountry.codeAlpha2,
                                        locale: locale
                                    }
                                },
                                locale: locale,
                                limit: 1,
                                populate: ['country']
                            })) || [];
                            const urlByTypeFound = urlByLocaleResponse[0];
                            if (urlByTypeFound) {
                                finalUrl = urlByTypeFound.url;
                            }
                        }
                    }
                }
            }
        }
        return finalUrl;
    };
    const replacePlaceholders = (template, mapping, originalElement) => {
        var _a;
        const country = (_a = originalElement.country) === null || _a === void 0 ? void 0 : _a[0].name;
        return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
            return mapping[key] ? country : match;
        });
    };
    const processSeo = async (seoSection, originalElement) => {
        var _a;
        let newSeoSection = seoSection;
        if (originalElement.customParentPage) {
            const parentFound = await ((_a = strapi.entityService) === null || _a === void 0 ? void 0 : _a.findOne('api::page.page', originalElement.customParentPage, {
                populate: ['seo']
            }));
            // console.log('parentFound', JSON.stringify(parentFound, null, 2));
            newSeoSection = parentFound === null || parentFound === void 0 ? void 0 : parentFound.seo;
        }
        // console.log('1 newSeoSection: ', newSeoSection);
        if (newSeoSection) {
            Object.entries(newSeoSection).forEach(([key, value]) => {
                if (typeof value === 'string' && value.includes('{{')) {
                    newSeoSection[key] = replacePlaceholders(value, NUGGETS_MAPPING, originalElement);
                }
            });
        }
        // console.log('2 newSeoSection: ', newSeoSection);
        return newSeoSection;
    };
    const processField = async (attributes, country, originalElement) => {
        var _a, _b, _c, _d;
        const fieldsAndValues = {};
        const stack = [{ attributes, fieldsAndValues, country, originalElement }];
        while (stack.length > 0) {
            // console.log('stack count:', count++);
            const { attributes, fieldsAndValues, country, originalElement } = stack.pop();
            // console.log('stack length:', stack.length);
            // console.log('attributes: ', JSON.stringify(attributes, null, 2))
            for (const field in attributes) {
                // console.log('field: ', field, attributes[field]);
                if (!UnWantedFields.includes(field)) {
                    if (Array.isArray(attributes[field]) && field === 'content') {
                        attributes.content = await processDynamicZoneContent(attributes[field], originalElement);
                    }
                    const isMediaField = IMAGE_ICON_PICTURE_FIELDS.includes(field) &&
                        Array.isArray(attributes[field])
                        ? ((_a = attributes[field][0]) === null || _a === void 0 ? void 0 : _a.alternativeText) !== undefined
                        : ((_b = attributes[field]) === null || _b === void 0 ? void 0 : _b.alternativeText) !== undefined;
                    if (field === 'url') {
                        fieldsAndValues.url = await processUrl(attributes[field], originalElement);
                    }
                    else if (field === 'variant') {
                        fieldsAndValues[field] = ((_c = attributes[field]) === null || _c === void 0 ? void 0 : _c.code) || ((_d = attributes[field]) === null || _d === void 0 ? void 0 : _d.name) || 'default';
                    }
                    else if (isMediaField) {
                        fieldsAndValues[field] = await processMediaFields(attributes[field], originalElement);
                    }
                    else if (typeof attributes[field] === 'string' && attributes[field].includes('<dynamic')) {
                        const newFields = await processNuggets({
                            markdowntext: attributes[field],
                            originalElement,
                            field,
                        });
                        // console.log('newFields:', JSON.stringify(newFields, null, 2));
                        Object.entries(newFields).forEach(([key, value]) => {
                            fieldsAndValues[key] = value;
                        });
                    }
                    else if (Array.isArray(attributes[field])) {
                        fieldsAndValues[field] = [];
                        for (let element of attributes[field]) {
                            if (typeof element === 'string') {
                                if (element.includes('<dynamic')) {
                                    const newFields = await processNuggets({
                                        markdowntext: element,
                                        originalElement,
                                        field,
                                    });
                                    element = newFields;
                                }
                                fieldsAndValues[field].push(element);
                                continue;
                            }
                            else if (typeof element === 'number') {
                                fieldsAndValues[field].push(element);
                                continue;
                            }
                            const processedElement = {};
                            stack.push({ attributes: element, fieldsAndValues: processedElement, country, originalElement });
                            fieldsAndValues[field].push(processedElement);
                        }
                    }
                    else if (!!attributes[field] && typeof attributes[field] === 'object') {
                        fieldsAndValues[field] = {};
                        stack.push({ attributes: attributes[field], fieldsAndValues: fieldsAndValues[field], country, originalElement });
                    }
                    else {
                        fieldsAndValues[field] = attributes[field];
                    }
                }
            }
            // console.log('fieldsAndValues: ', JSON.stringify(fieldsAndValues, null, 2));
        }
        return fieldsAndValues;
    };
    const processFields = async (attributes, fieldsAndValues, country, originalElement) => {
        var _a;
        for (const field in attributes) {
            // console.log('field: ', field);
            if (!UnWantedFields.includes(field)) {
                if (Array.isArray(attributes[field]) && field === 'content') {
                    // attributes.content = await processDynamicZoneContent(attributes[field], originalElement);
                }
                if (field === 'seo') {
                    fieldsAndValues.seo = await processSeo(attributes.seo, originalElement);
                }
                else if (field === 'layout') {
                    fieldsAndValues.layout = { id: (_a = attributes[field]) === null || _a === void 0 ? void 0 : _a.id };
                }
                else {
                    const response = await processField({ [field]: attributes[field] }, country, originalElement);
                    fieldsAndValues[field] = response[field];
                }
            }
        }
        // console.log('fieldsAndValues: ', JSON.stringify(fieldsAndValues, null, 2));
        return fieldsAndValues;
    };
    const makeCopyFromOriginal = async ({ model, original, copy, country, }) => {
        var _a;
        if (!original) {
            return null;
        }
        const service = (_a = original.type) === null || _a === void 0 ? void 0 : _a.replace('Custom', '');
        const locale = original.locale.code;
        const attributes = original.attributes || original;
        // console.log('makeCopyFromOriginal attributes:', attributes);
        let newCopy = { ...copy };
        if (!copy) {
            newCopy = {
                country: `${country}` || '',
                location: original.locale,
                referenceId: original.id,
                fieldsAndValues: {},
                model: '',
                url: '',
                referenceName: original.name,
            };
        }
        if (model) {
            newCopy.model = model;
            //newCopy.identifier = `${model}::${country || ''}::${service || ''}::${locale || original.locale || ''}`.replace(/:/g, '-');
            newCopy.identifier = `${model}::${original.id}::${country || ''}::${service || ''}::${locale || original.locale || ''}`.replace(/:/g, '-');
        }
        newCopy.fieldsAndValues = {};
        newCopy.publishedAt = original.publishedAt;
        const fieldsAndValues = {};
        await processFields(attributes, fieldsAndValues, country, original);
        newCopy.fieldsAndValues = fieldsAndValues;
        newCopy.referenceName = `${original.title || 'no title found'} ${country || ''}`;
        newCopy.includeInSitemap = attributes.includeInSitemap === true;
        return newCopy;
    };
    const removeDynamictag = (dynamidTag, value) => {
        var _a;
        try {
            console.log('1 dynamidTag: ', dynamidTag);
            console.log('value: ( ', value, ' )');
            // If the text is empty or doesn't contain any dynamic tags, return the value directly
            if (!dynamidTag) {
                return value || '';
            }
            // Use the utility function to replace dynamic tags
            dynamidTag.replaceWith(value || '');
            return value;
        }
        catch (error) {
            console.error('Error in removeDynamictag:', error);
            const errorDetails = {
                message: error.message,
                stack: error.stack,
                ...(((_a = error.response) === null || _a === void 0 ? void 0 : _a.data) && { responseData: error.response.data }),
                name: error.name,
                code: error.code
            };
            console.error('Error details:', JSON.stringify(errorDetails, null, 2));
            // If you need to log the full error object separately
            console.error('Full error object:', error);
            return value || '';
        }
    };
    const processNuggets = async ({ markdowntext, originalElement, field, }) => {
        var _a, _b, _c;
        try {
            const pageCountry = getCountryFromPage(originalElement);
            const pageLocale = originalElement.locale || '';
            const countryName = (pageCountry === null || pageCountry === void 0 ? void 0 : pageCountry.name) || '';
            const extraFields = {};
            // Extract all dynamic tags from the content
            const { document } = (0, linkedom_1.parseHTML)(`<div>${markdowntext}</div>`);
            const dynamicTags = document.querySelectorAll('dynamic');
            // Create nuggets array for processing
            const nuggets = [];
            // Process each dynamic tag to create nuggets
            for (const tag of dynamicTags) {
                try {
                    const data = tag.getAttribute('data');
                    if (!data)
                        continue;
                    const { id = null, country = '', locale = '', type = 'none', name = '' } = data
                        ? typeof data === 'string'
                            ? JSON.parse(data)
                            : data
                        : {};
                    console.log('processNuggets id:', id, ' type:', type, ' name:', name, ' country:', country, ' locale:', locale);
                    if (!id)
                        continue;
                    let value = ' ';
                    if (type === 'internal') {
                        value = processInternalDynamicData({ country: pageCountry, id, name }) || ' ';
                    }
                    else {
                        const nuggetResponses = await ((_a = strapi.entityService) === null || _a === void 0 ? void 0 : _a.findMany('api::nugget-value.nugget-value', {
                            filters: {
                                referenceId: id,
                            },
                        }));
                        // console.log('processNuggets nuggetResponses:', nuggetResponses);
                        const nuggetResponse = nuggetResponses === null || nuggetResponses === void 0 ? void 0 : nuggetResponses.find((nugget) => {
                            let identifier = `${type}::${id}::${country ? countryName : ''}::${locale ? pageLocale : ''}`;
                            // console.log('processNuggets identifier:', identifier);
                            // console.log('processNuggets nugget.identifier:', nugget.identifier);
                            return nugget.identifier === identifier;
                        });
                        // console.log('processNuggets nuggetResponse:', nuggetResponse);
                        value = (nuggetResponse === null || nuggetResponse === void 0 ? void 0 : nuggetResponse.value) || ' ';
                    }
                    tag.replaceWith(value || '');
                    // Create a nugget object
                    nuggets.push({
                        id: data,
                        content: value
                    });
                    // Store the value in extraFields for later use
                    if (value && `${value}`.length < 100) {
                        extraFields[name] = value;
                    }
                }
                catch (error) {
                    console.error('Error processing dynamic tag:', error);
                    const errorDetails = {
                        message: error.message,
                        stack: error.stack,
                        ...(((_b = error.response) === null || _b === void 0 ? void 0 : _b.data) && { responseData: error.response.data }),
                        name: error.name,
                        code: error.code
                    };
                    console.error('Error details:', JSON.stringify(errorDetails, null, 2));
                    // If you need to log the full error object separately
                    console.error('Full error object:', error);
                    continue;
                }
            }
            let newContent = (0, html_entities_1.decode)(document.documentElement.outerHTML);
            // console.log('1 final newContent: ', newContent);
            newContent = newContent.replace('<div>', '');
            newContent = newContent.replace('</div>', '');
            return {
                ...extraFields,
                [field]: newContent,
            };
        }
        catch (error) {
            console.error('Error in processNuggets:', error);
            const errorDetails = {
                message: error.message,
                stack: error.stack,
                ...(((_c = error.response) === null || _c === void 0 ? void 0 : _c.data) && { responseData: error.response.data }),
                name: error.name,
                code: error.code
            };
            console.error('Error details:', JSON.stringify(errorDetails, null, 2));
            // If you need to log the full error object separately
            console.error('Full error object:', error);
            return {
                [field]: markdowntext
            };
        }
    };
    const sanitizeCountryName = (countryName = '') => {
        if (!countryName) {
            return '';
        }
        // Normalize the string to remove diacritics (like accents)
        const normalized = countryName.normalize("NFD").replace(/[\u0300-\u036f]/g, "");
        // Replace spaces with hyphens
        const hyphenated = (normalized === null || normalized === void 0 ? void 0 : normalized.replace(/\s+/g, "-")) || '';
        // Remove all non-alphanumeric characters except hyphens
        const slug = (hyphenated === null || hyphenated === void 0 ? void 0 : hyphenated.replace(/[^a-zA-Z0-9-]/g, "")) || '';
        // Convert to lowercase for URL consistency
        return (slug === null || slug === void 0 ? void 0 : slug.toLowerCase()) || '';
    };
    const findStaticContentByPage = async (page) => {
        var _a, _b, _c;
        let url = page.url;
        const country = (_a = page.country) === null || _a === void 0 ? void 0 : _a[0];
        const countryCode = (country === null || country === void 0 ? void 0 : country.codeAlpha2) || 'default';
        // console.log('findStaticContentByPage countryCode: ', countryCode);
        if (countryCode !== 'default') {
            const urlsFound = await ((_b = strapi.entityService) === null || _b === void 0 ? void 0 : _b.findMany('api::url.url', {
                filters: {
                    pageType: page.type.replace('Custom', ''),
                    country: {
                        codeAlpha2: countryCode,
                        locale: page === null || page === void 0 ? void 0 : page.locale
                    }
                },
                locale: page === null || page === void 0 ? void 0 : page.locale,
                populate: ['country']
            })) || [];
            const urlFound = urlsFound[0];
            // console.log('findStaticContentByPage urlFound: ', urlFound);
            if (urlFound) {
                url = urlFound.url;
            }
        }
        console.log('findStaticContentByPage url: ', url);
        if (!url) {
            return null;
        }
        const elementsFound = await ((_c = strapi.entityService) === null || _c === void 0 ? void 0 : _c.findMany('api::static-content.static-content', {
            filters: {
                url
            },
            locale: page.locale
        })) || [];
        const elementFound = elementsFound === null || elementsFound === void 0 ? void 0 : elementsFound[0];
        console.log('findStaticContentByPage elementFound: ', elementFound === null || elementFound === void 0 ? void 0 : elementFound.id);
        return elementFound;
    };
    const processOriginalToStaticContent = async (originalElement, model) => {
        var _a, _b, _c, _d;
        // const pageType = originalElement.type;
        const country = (_a = originalElement.country) === null || _a === void 0 ? void 0 : _a[0];
        // console.log('processOriginalToStaticContent country:', JSON.stringify(country, null, 2));
        const countryCode = (country === null || country === void 0 ? void 0 : country.codeAlpha2) || 'default';
        // process the content
        let newStaticContent = await makeCopyFromOriginal({
            original: originalElement,
            model: model,
            country: countryCode,
            copy: {}
        });
        // console.log('1 processOriginalToStaticContent newStaticContent:', JSON.stringify(newStaticContent, null, 2));
        if (!newStaticContent) {
            return;
        }
        newStaticContent.referenceId = originalElement.id;
        newStaticContent.url = originalElement.url;
        newStaticContent.country = countryCode;
        (_b = newStaticContent.fieldsAndValues) === null || _b === void 0 ? true : delete _b.url;
        (_c = newStaticContent.fieldsAndValues) === null || _c === void 0 ? true : delete _c.country;
        if (countryCode !== 'default') {
            const urlsFound = await ((_d = strapi.entityService) === null || _d === void 0 ? void 0 : _d.findMany('api::url.url', {
                filters: {
                    pageType: originalElement.type.replace('Custom', ''),
                    country: {
                        codeAlpha2: countryCode,
                        locale: originalElement === null || originalElement === void 0 ? void 0 : originalElement.locale
                    }
                },
                locale: originalElement === null || originalElement === void 0 ? void 0 : originalElement.locale,
                populate: ['country']
            })) || [];
            const urlFound = urlsFound[0];
            if (urlFound) {
                newStaticContent.url = urlFound.url;
            }
        }
        // console.log('2 processOriginalToStaticContent newStaticContent:', JSON.stringify(newStaticContent, null, 2));
        if (AZURE_CDN_ENDPOINT_TO_KEEP && AZURE_CDN_ENDPOINT_TO_REPLACE) {
            // console.log('replcaging....')
            const regex = new RegExp(AZURE_CDN_ENDPOINT_TO_REPLACE, 'g');
            newStaticContent.fieldsAndValues = JSON.parse(JSON.stringify(newStaticContent.fieldsAndValues).replace(regex, AZURE_CDN_ENDPOINT_TO_KEEP));
            //console.log('newStaticContent.fieldsAndValues: ', JSON.stringify(newStaticContent.fieldsAndValues, null, 2));
        }
        const elementSaved = await createOrUpdateStaticContent({
            id: (newStaticContent === null || newStaticContent === void 0 ? void 0 : newStaticContent.id) || 0,
            query: {
                data: {
                    ...newStaticContent,
                    locale: originalElement === null || originalElement === void 0 ? void 0 : originalElement.locale,
                }
            }
        });
        // console.log('processOriginalToStaticContent elementSaved:', elementSaved);
        // return new process content
        return newStaticContent;
    };
    const processElement = async (parameters) => {
        var _a, _b, _c;
        console.log('processElement parameters:', parameters.referenceId, parameters.model, parameters.country);
        const { model, referenceId, country } = parameters;
        const originalElement = await ((_a = strapi.entityService) === null || _a === void 0 ? void 0 : _a.findOne(model, referenceId, {
            // populate[content][on][sections.custom][populate][custom_components][populate][0]=content
            populate: [
                'deep'
            ],
        }));
        console.log('processElement originalElement:', originalElement === null || originalElement === void 0 ? void 0 : originalElement.title, originalElement === null || originalElement === void 0 ? void 0 : originalElement.type);
        if (!originalElement) {
            return;
        }
        else if (country) {
            const countryId = Array.isArray(country) ? (_b = country[0]) === null || _b === void 0 ? void 0 : _b.id : country.id;
            if (countryId) {
                const countryFound = await ((_c = strapi.entityService) === null || _c === void 0 ? void 0 : _c.findOne('api::core-country.core-country', countryId));
                console.log('processElement countryFound:', countryFound === null || countryFound === void 0 ? void 0 : countryFound.name);
                originalElement.country = [countryFound];
            }
        }
        // process the content
        const newStaticContent = await processOriginalToStaticContent(originalElement, model);
        // return new process content
        return newStaticContent;
    };
    const cleanStaticContent = async (originalPage) => {
    };
    return {
        detectDynamicDataElements,
        processDynamicZoneContent,
        makeCopyFromOriginal,
        processNuggets,
        processElement,
        cleanStaticContent,
        createOrUpdateStaticContent,
        findStaticContentByPage,
    };
};
/* {
  content: {
    on: {
      'sections.custom': {
        populate: {
          'custom_components': {
            populate: {
              content: {
                populate: '*'
              }
            }
          }
        }
      },
      "sections.heading": {
        populate: '*'
      },
      "sections.hero": {
        populate: '*'
      },
      "sections.banner": {
        populate: '*'
      },
      "sections.action-list": {
        populate: '*'
      },
      "sections.heading-list": {
        populate: '*'
      },
      "sections.feature-list": {
        populate: '*'
      },
      "sections.text-list": {
        populate: '*'
      },
      "sections.country-flag": {
        populate: '*'
      },
      "sections.fake-msg": {
        populate: '*'
      },
      "sections.faq": {
        populate: '*'
      },
      "sections.review": {
        populate: '*'
      },
      "sections.cta": {
        populate: '*'
      },
      "sections.logo-list": {
        populate: '*'
      },
      "sections.news": {
        populate: '*'
      },
      "sections.promotions": {
        populate: '*'
      },
      "sections.partners-list": {
        populate: '*'
      },
      "sections.distributors-list": {
        populate: '*'
      },
      "sections.operators-list": {
        populate: '*'
      },
      "sections.available-countries-by-service-types": {
        populate: '*'
      },
      "sections.feature": {
        populate: '*'
      }
    },
  },
  seo: {
    populate: [
      "metaSocial",
      "metaSocial.image",

      "metaImage"
    ]
  },
  localizations: { populate: '*' },

  navbar: {
    populate: [
      "logo",
      "logo.iconImage",
      "logo.url",

      "actionLists",
      "actionLists.actions",
      "actionLists.actions.iconImage",
      "actionLists.actions.url",
    ]
  },
  footer: {
    populate: [
      "primaryLogo",
      "primaryLogo.logo",

      "paymentLogos",
      "paymentLogos.logos",
      "paymentLogos.logos.logo",

      "linkGroup",
      "linkGroup.actions",
      "linkGroup.actions.iconImage",
      "linkGroup.actions.url",

      "disclaimerSection",

      "secureInformationLogos",
      "secureInformationLogos.logos",
      "secureInformationLogos.logos.logo",

      "footerLinks",
      "footerLinks.actions",
      "footerLinks.actions.iconImage",
      "footerLinks.actions.url",

      "socialLinks",
      "socialLinks.actions",
      "socialLinks.actions.iconImage",
      "socialLinks.actions.url",
    ]
  },
  layout: { populate: '*' }

} */
