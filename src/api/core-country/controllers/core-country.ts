/**
 * core-country controller
 */

import { factories } from '@strapi/strapi'

export default factories.createCoreController(
    'api::core-country.core-country' as any,
    ({ strapi }) => ({
        async find(ctx) {
            const sanitizedQueryParams: any = await this.sanitizeQuery(ctx);
            console.log('sanitizedQueryParams: ', sanitizedQueryParams);

            const activeServices: any = [];
            if (sanitizedQueryParams.filters?.activeServices) {
                activeServices.push(...sanitizedQueryParams.filters.activeServices.split(','));
                delete sanitizedQueryParams.filters.activeServices;
            } else if (sanitizedQueryParams.activeServices) {
                activeServices.push(...sanitizedQueryParams.activeServices.split(','));
            }

            // Extract pagination parameters
            const { page = 1, pageSize = 25, start, limit } = sanitizedQueryParams;
            const pageNumber = parseInt(page as string, 10);
            const pageSizeNumber = parseInt(pageSize as string, 10);
            const limitValue = limit || pageSizeNumber;

            // Remove pagination params from filters to avoid conflicts
            const { page: _, pageSize: __, start: ___, limit: ____, ...queryWithoutPagination } = sanitizedQueryParams;

            // Get all countries that match the filters (without pagination)
            const allCountries = await strapi.entityService?.findMany('api::core-country.core-country', {
                ...queryWithoutPagination,
                limit: -1 // Get all countries
            } as any);

            console.log('allCountries count: ', allCountries?.length || 0);

            let countriesWithActiveServices: any[] = [];

            // If services-urls is requested, filter countries by active services
            if (sanitizedQueryParams['services-urls'] === 'true') {
                // Get URLs for all countries and filter out those without active services
                const countriesWithUrls = await Promise.all(
                    (allCountries || []).map(async (country) => {
                        const urls = await strapi.plugins['static-content-plugin'].services.manageUrls.getUrlsByCountry({
                            country,
                            activeServices
                        });

                        return {
                            country,
                            urls,
                            hasActiveServices: Object.keys(urls).length > 0
                        };
                    })
                );

                // Filter to only include countries with active services
                countriesWithActiveServices = countriesWithUrls
                    .filter(item => item.hasActiveServices)
                    .map(item => ({
                        ...item.country,
                        urls: item.urls
                    }));
            } else {
                // If services-urls is not requested, use all countries
                countriesWithActiveServices = allCountries || [];
            }

            console.log('countriesWithActiveServices count: ', countriesWithActiveServices.length);

            // Apply pagination to filtered results
            const startValue = start || (pageNumber - 1) * limitValue;
            const paginatedResults = countriesWithActiveServices.slice(startValue, startValue + limitValue);

            // Calculate pagination metadata based on filtered results
            const totalFiltered = countriesWithActiveServices.length;
            const pageCount = Math.ceil(totalFiltered / limitValue);
            const pagination = {
                page: pageNumber,
                pageSize: limitValue,
                pageCount,
                total: totalFiltered
            };

            console.log('pagination: ', JSON.stringify(pagination, null, 2));

            let sanitizedResults: any = await this.sanitizeOutput(paginatedResults, ctx);

            return this.transformResponse(sanitizedResults, { pagination });
        },
    })
);
