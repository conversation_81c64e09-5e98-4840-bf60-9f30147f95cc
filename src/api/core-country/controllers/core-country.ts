/**
 * core-country controller
 */

import { factories } from '@strapi/strapi'

export default factories.createCoreController(
    'api::core-country.core-country' as any,
    ({ strapi }) => ({
        async find(ctx) {
            const sanitizedQueryParams: any = await this.sanitizeQuery(ctx);
            console.log('sanitizedQueryParams: ', sanitizedQueryParams);

            const activeServices: any = [];
            if (sanitizedQueryParams.filters?.activeServices) {
                activeServices.push(...sanitizedQueryParams.filters.activeServices.split(','));
                delete sanitizedQueryParams.filters.activeServices;
            } else if (sanitizedQueryParams.activeServices) {
                activeServices.push(...sanitizedQueryParams.activeServices.split(','));
            }

            // Extract pagination parameters
            const { page = 1, pageSize = 25, start, limit } = sanitizedQueryParams;
            const pageNumber = parseInt(page as string, 10);
            const pageSizeNumber = parseInt(pageSize as string, 10);
            const startValue = start || (pageNumber - 1) * pageSizeNumber;
            const limitValue = limit || pageSizeNumber;

            // Remove pagination params from filters to avoid conflicts
            const { page: _, pageSize: __, start: ___, limit: ____, ...queryWithoutPagination } = sanitizedQueryParams;

            // Use Promise.all to execute count and findMany concurrently for better performance
            const [results, total] = await Promise.all([
                strapi.entityService?.findMany('api::core-country.core-country', {
                    ...queryWithoutPagination,
                    start: startValue,
                    limit: limitValue
                } as any),
                strapi.entityService?.count('api::core-country.core-country', {
                    ...queryWithoutPagination
                } as any)
            ]);

            console.log('result: ', JSON.stringify(results, null, 2));

            // Calculate pagination metadata
            const pageCount = Math.ceil((total || 0) / limitValue);
            const pagination = {
                page: pageNumber,
                pageSize: limitValue,
                pageCount,
                total: total || 0
            };

            let sanitizedResults: any = await this.sanitizeOutput(results, ctx);
            if (sanitizedQueryParams['services-urls'] === 'true') {
                sanitizedResults = await Promise.all(sanitizedResults.map(async (country) => {
                    return {
                        ...country,
                        urls: {
                            ...(await strapi.plugins['static-content-plugin'].services.manageUrls.getUrlsByCountry({country, activeServices }))
                        }
                    }
                }))
            }

            return this.transformResponse(sanitizedResults, { pagination });
        },
    })
);
